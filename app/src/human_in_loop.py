"""
Human-in-the-loop system for AI agent tool execution.
Manages user confirmations and approvals for tool calls.
"""
import asyncio
import uuid
from typing import Dict, Any, Optional, Callable, Awaitable
from datetime import datetime, timedelta
import logging
from .tool_schemas import (
    ToolCallRequest, ToolCallResponse, ToolExecutionStatus, 
    ToolRiskLevel, get_tool_metadata, validate_tool_input
)

logger = logging.getLogger(__name__)


class PendingToolCall:
    """Represents a tool call awaiting user confirmation."""
    
    def __init__(self, request: ToolCallRequest, callback: Callable[[bool], Awaitable[Any]]):
        self.request = request
        self.callback = callback
        self.created_at = datetime.now()
        self.status = ToolExecutionStatus.PENDING
        self.future = asyncio.Future()
    
    async def approve(self) -> Any:
        """Approve the tool call and execute it."""
        self.status = ToolExecutionStatus.APPROVED
        try:
            result = await self.callback(True)
            self.status = ToolExecutionStatus.COMPLETED
            self.future.set_result(result)
            return result
        except Exception as e:
            self.status = ToolExecutionStatus.FAILED
            self.future.set_exception(e)
            raise
    
    async def reject(self, reason: str = "User rejected") -> None:
        """Reject the tool call."""
        self.status = ToolExecutionStatus.REJECTED
        self.future.set_result({"success": False, "error": reason})
    
    def is_expired(self, timeout_minutes: int = 5) -> bool:
        """Check if the tool call request has expired."""
        return datetime.now() - self.created_at > timedelta(minutes=timeout_minutes)


class HumanInLoopManager:
    """Manages human-in-the-loop interactions for tool execution."""
    
    def __init__(self, timeout_minutes: int = 5):
        self.pending_calls: Dict[str, PendingToolCall] = {}
        self.timeout_minutes = timeout_minutes
        self._cleanup_task = None
        self._start_cleanup_task()
    
    def _start_cleanup_task(self):
        """Start the cleanup task for expired requests."""
        if self._cleanup_task is None or self._cleanup_task.done():
            self._cleanup_task = asyncio.create_task(self._cleanup_expired_calls())
    
    async def _cleanup_expired_calls(self):
        """Periodically clean up expired tool call requests."""
        while True:
            try:
                await asyncio.sleep(60)  # Check every minute
                expired_ids = []
                
                for request_id, pending_call in self.pending_calls.items():
                    if pending_call.is_expired(self.timeout_minutes):
                        expired_ids.append(request_id)
                        try:
                            await pending_call.reject("Request expired")
                        except Exception as e:
                            logger.error(f"Error rejecting expired call {request_id}: {e}")
                
                for request_id in expired_ids:
                    self.pending_calls.pop(request_id, None)
                    logger.info(f"Cleaned up expired tool call: {request_id}")
                    
            except Exception as e:
                logger.error(f"Error in cleanup task: {e}")
                await asyncio.sleep(60)
    
    async def request_tool_execution(
        self, 
        tool_name: str, 
        parameters: Dict[str, Any], 
        session_id: str,
        user_message: str,
        execute_callback: Callable[[bool], Awaitable[Any]]
    ) -> ToolCallResponse:
        """
        Request tool execution with potential human confirmation.
        
        Args:
            tool_name: Name of the tool to execute
            parameters: Tool parameters
            session_id: User session ID
            user_message: Original user message
            execute_callback: Callback function to execute the tool
            
        Returns:
            ToolCallResponse with execution status and details
        """
        request_id = str(uuid.uuid4())
        
        try:
            # Validate tool parameters
            validated_params = validate_tool_input(tool_name, parameters)
            
            # Get tool metadata
            metadata = get_tool_metadata(tool_name)
            if not metadata:
                logger.warning(f"No metadata found for tool: {tool_name}")
                # Execute immediately for unknown tools (backward compatibility)
                result = await execute_callback(True)
                return ToolCallResponse(
                    request_id=request_id,
                    tool_name=tool_name,
                    status=ToolExecutionStatus.COMPLETED,
                    parameters=validated_params,
                    result=result,
                    risk_level=ToolRiskLevel.SAFE
                )
            
            # Create tool call request
            request = ToolCallRequest(
                tool_name=tool_name,
                parameters=validated_params,
                session_id=session_id,
                request_id=request_id,
                user_message=user_message
            )
            
            # Check if confirmation is required
            if metadata.requires_confirmation or metadata.risk_level != ToolRiskLevel.SAFE:
                # Store pending call for user confirmation
                pending_call = PendingToolCall(request, execute_callback)
                self.pending_calls[request_id] = pending_call
                
                confirmation_msg = metadata.confirmation_message or self._generate_confirmation_message(
                    tool_name, validated_params, metadata.risk_level
                )
                
                return ToolCallResponse(
                    request_id=request_id,
                    tool_name=tool_name,
                    status=ToolExecutionStatus.PENDING,
                    parameters=validated_params,
                    requires_confirmation=True,
                    confirmation_message=confirmation_msg,
                    risk_level=metadata.risk_level,
                    metadata={"created_at": pending_call.created_at.isoformat()}
                )
            else:
                # Execute immediately for safe tools
                try:
                    result = await execute_callback(True)
                    return ToolCallResponse(
                        request_id=request_id,
                        tool_name=tool_name,
                        status=ToolExecutionStatus.COMPLETED,
                        parameters=validated_params,
                        result=result,
                        risk_level=metadata.risk_level
                    )
                except Exception as e:
                    logger.error(f"Tool execution failed: {e}")
                    return ToolCallResponse(
                        request_id=request_id,
                        tool_name=tool_name,
                        status=ToolExecutionStatus.FAILED,
                        parameters=validated_params,
                        risk_level=metadata.risk_level,
                        metadata={"error": str(e)}
                    )
                    
        except ValueError as e:
            # Validation error
            return ToolCallResponse(
                request_id=request_id,
                tool_name=tool_name,
                status=ToolExecutionStatus.FAILED,
                parameters=parameters,
                risk_level=ToolRiskLevel.SAFE,
                metadata={"validation_error": str(e)}
            )
        except Exception as e:
            logger.error(f"Unexpected error in tool execution request: {e}")
            return ToolCallResponse(
                request_id=request_id,
                tool_name=tool_name,
                status=ToolExecutionStatus.FAILED,
                parameters=parameters,
                risk_level=ToolRiskLevel.SAFE,
                metadata={"error": str(e)}
            )
    
    async def approve_tool_call(self, request_id: str) -> Dict[str, Any]:
        """Approve a pending tool call."""
        pending_call = self.pending_calls.get(request_id)
        if not pending_call:
            raise ValueError(f"No pending tool call found with ID: {request_id}")
        
        if pending_call.is_expired(self.timeout_minutes):
            await pending_call.reject("Request expired")
            self.pending_calls.pop(request_id, None)
            raise ValueError(f"Tool call request {request_id} has expired")
        
        try:
            result = await pending_call.approve()
            self.pending_calls.pop(request_id, None)
            return result
        except Exception as e:
            self.pending_calls.pop(request_id, None)
            raise
    
    async def reject_tool_call(self, request_id: str, reason: str = "User rejected") -> None:
        """Reject a pending tool call."""
        pending_call = self.pending_calls.get(request_id)
        if not pending_call:
            raise ValueError(f"No pending tool call found with ID: {request_id}")
        
        await pending_call.reject(reason)
        self.pending_calls.pop(request_id, None)
    
    def get_pending_calls(self, session_id: Optional[str] = None) -> Dict[str, Dict[str, Any]]:
        """Get all pending tool calls, optionally filtered by session."""
        result = {}
        for request_id, pending_call in self.pending_calls.items():
            if session_id is None or pending_call.request.session_id == session_id:
                result[request_id] = {
                    "tool_name": pending_call.request.tool_name,
                    "parameters": pending_call.request.parameters,
                    "session_id": pending_call.request.session_id,
                    "user_message": pending_call.request.user_message,
                    "created_at": pending_call.created_at.isoformat(),
                    "status": pending_call.status.value,
                    "is_expired": pending_call.is_expired(self.timeout_minutes)
                }
        return result
    
    def _generate_confirmation_message(self, tool_name: str, parameters: Dict[str, Any], risk_level: ToolRiskLevel) -> str:
        """Generate a confirmation message for tool execution."""
        risk_emoji = {
            ToolRiskLevel.SAFE: "✅",
            ToolRiskLevel.MODERATE: "⚠️",
            ToolRiskLevel.HIGH: "🚨"
        }
        
        param_summary = ", ".join([f"{k}: {v}" for k, v in list(parameters.items())[:3]])
        if len(parameters) > 3:
            param_summary += f" (and {len(parameters) - 3} more)"
        
        return f"{risk_emoji.get(risk_level, '❓')} **Tool Execution Request**\n\n" \
               f"**Tool:** {tool_name}\n" \
               f"**Risk Level:** {risk_level.value.title()}\n" \
               f"**Parameters:** {param_summary}\n\n" \
               f"Do you want to proceed with this tool execution?"


# Global instance
human_in_loop_manager = HumanInLoopManager()
